Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8496E0000 ntdll.dll
7FF849050000 KERNEL32.DLL
7FF846B70000 KERNELBASE.dll
7FF843A70000 apphelp.dll
7FF848240000 USER32.dll
7FF8470E0000 win32u.dll
7FF8493A0000 GDI32.dll
7FF847190000 gdi32full.dll
7FF847420000 msvcp_win.dll
7FF846F00000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF848F40000 advapi32.dll
7FF848500000 msvcrt.dll
7FF849270000 sechost.dll
7FF8493D0000 RPCRT4.dll
7FF8463E0000 CRYPTBASE.DLL
7FF847110000 bcryptPrimitives.dll
7FF847CB0000 IMM32.DLL
